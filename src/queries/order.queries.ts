/* eslint-disable @typescript-eslint/no-explicit-any */

'use client';

import { useApi } from '@/hooks/use-api';
import { ActiveOrder, ActiveOrdersResponse } from '@/types/order';
import { getWidgetMap } from '@/utils/get-widget-map';
import { useGlobalDataStore } from '@/store/global-data.store';
import { useApiQuery } from '@/hooks/use-api-query';
import { getCookie } from 'cookies-next/client';

const formatActiveOrders = (data: any): ActiveOrder[] => {
  const widgetMap = getWidgetMap(data);
  if (!widgetMap['active_orders'] || !widgetMap['active_orders']['bottom_sticky_notifications']) {
    return [];
  }
  const notifications = widgetMap['active_orders']['bottom_sticky_notifications'].map(
    (notificationBlockId: string) => {
      return widgetMap[notificationBlockId];
    }
  );
  return notifications;
};

/**
 * Hook for fetching active orders with React Query and polling
 */
export function useActiveOrders() {
  const { apiClient } = useApi();

  const { globalData } = useGlobalDataStore();
  const refreshToken = getCookie('x-refresh-token');

  return useApiQuery<ActiveOrdersResponse>({
    queryKey: ['orders', 'active'],
    queryFn: async () => {
      try {
        const data = await apiClient<any>({
          path: '/orders/active',
          method: 'GET',
        });
        if (data.component_data) {
          const formattedData = formatActiveOrders(data);
          return {
            active_orders: formattedData,
            tokens: data.tokens,
            timestamp: data.timestamp,
          };
        }
        return {
          active_orders: [],
          tokens: data.tokens,
          timestamp: data.timestamp,
        };
      } catch (error) {
        console.error('Error fetching active orders:', error);
        throw error;
      }
    },
    refetchInterval: 1000 * 5, // Poll every 5 seconds
    refetchOnWindowFocus: true,
    retry: 2,
    enabled: !!globalData.user && !!refreshToken,
  });
}
