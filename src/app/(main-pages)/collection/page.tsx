import { getBrandsData, getManData, getWomanData } from '@/actions/collection.actions';
import CollectionTabs from '@/components/collection/collection-tabs';
import { getQueryClient } from '@/get-query-client';
import { getMetadata } from '@/utils/helper';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import { getCookie, setCookie } from 'cookies-next/server';
import { cookies } from 'next/headers';
import { getInitialCollectionData } from '@/actions/collection.actions';

export const metadata: Metadata = getMetadata(
  'KNOT',
  'Get fashion in 60-mins. Try before you buy. No waits or regrets, just fire fits!',
  'https://ik.imagekit.io/slickapp/droplet/tr:dpr-2,f-webp:/app_images/1200X630.png?ik-t=9999999999&ik-s=2e4734befc0f9546f1095894dd7bf8f7b9e3e464'
);

interface CollectionProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function Collection({ searchParams }: CollectionProps) {
  const params = await searchParams;
  const shopParam = params.shop as string | undefined;

  // Sync X-User-Pref cookie with URL parameter if present
  if (shopParam && (shopParam === 'man' || shopParam === 'woman')) {
    const currentCookie = await getCookie('X-User-Pref', { cookies });
    if (currentCookie !== shopParam) {
      await setCookie('X-User-Pref', shopParam, { cookies });
    }
  }

  const isBrandSelected = await getCookie('X-Is-Brand-Selected', { cookies });
  const queryClient = getQueryClient();

  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ['pages', 'collection', 'man'],
      queryFn: async () => await getManData(),
    }),
    queryClient.prefetchQuery({
      queryKey: ['pages', 'collection', 'woman'],
      queryFn: async () => await getWomanData(),
    }),
    queryClient.prefetchQuery({
      queryKey: ['pages', 'collection', 'brands'],
      queryFn: async () => await getBrandsData(),
    }),
    queryClient.prefetchQuery({
      queryKey: ['pages', 'collection', 'initial'],
      queryFn: async () => await getInitialCollectionData(),
    }),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <div className="flex-1 overflow-y-clip main-page z-50">
        <CollectionTabs defaultTab={isBrandSelected === 'true' ? 'brands' : shopParam} />
      </div>
    </HydrationBoundary>
  );
}
