import { Metadata } from 'next';
import { getQueryClient } from '@/get-query-client';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getHomeData } from '@/actions/home.actions';
import { getCookie, setCookie } from 'cookies-next/server';
import { cookies } from 'next/headers';
import { getInitialHomeData } from '@/actions/home.actions';

import HomeNavbar from '@/components/home/<USER>';
import HomeTabs from '@/components/home/<USER>';
import NotificationCarousel from '@/components/home/<USER>';
import { updateDeliveryLocation } from '@/actions/location.actions';
import { GET_INITIAL_LOCATION_DATA_QUERY_KEY } from '@/queries/location.queries';
import DragScrollContainer from '@/components/common/drag-scroll-container';

export const metadata: Metadata = {
  title: 'KNOT',
  description: 'Get fashion in 60-mins. Try before you buy. No waits or regrets, just fire fits!',
  openGraph: {
    title: 'KNOT',
    description: 'Get fashion in 60-mins. Try before you buy. No waits or regrets, just fire fits!',
    url: 'https://knotnow.co/',
    type: 'website',
    images: [
      {
        url: 'https://ik.imagekit.io/slickapp/droplet/tr:dpr-2,f-webp:/app_images/1200X630.png?ik-t=9999999999&ik-s=2e4734befc0f9546f1095894dd7bf8f7b9e3e464',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'KNOT',
    description: 'Get fashion in 60-mins. Try before you buy. No waits or regrets, just fire fits!',
    images: [
      'https://ik.imagekit.io/slickapp/droplet/tr:dpr-2,f-webp:/app_images/1200X630.png?ik-t=9999999999&ik-s=2e4734befc0f9546f1095894dd7bf8f7b9e3e464',
    ],
  },
  icons: {
    icon: '/favicon.ico',
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'black-translucent',
    title: 'KNOT',
  },
  other: {
    'apple-mobile-web-app-title': 'KNOT',
  },
};

// Define the props type for the Home page
interface HomeProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function Home({ searchParams }: HomeProps) {
  const params = await searchParams;
  const shopParam = params.shop as string | undefined;
  const queryClient = getQueryClient();

  // Sync X-User-Pref cookie with URL parameter if present
  if (shopParam && (shopParam === 'man' || shopParam === 'woman')) {
    const currentCookie = await getCookie('X-User-Pref', { cookies });
    if (currentCookie !== shopParam) {
      await setCookie('X-User-Pref', shopParam, { cookies });
    }
  }

  const currentAddress = await getCookie('X-Address-Id', { cookies });

  if (!currentAddress) {
    await queryClient.prefetchQuery({
      queryKey: [GET_INITIAL_LOCATION_DATA_QUERY_KEY],
      queryFn: async () => await updateDeliveryLocation({}),
    });
  }

  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ['pages', 'home', 'initial'],
      queryFn: async () => await getInitialHomeData(),
    }),
    queryClient.prefetchQuery({
      queryKey: ['pages', 'home', 'man'],
      queryFn: async () => await getHomeData('men'),
    }),
    queryClient.prefetchQuery({
      queryKey: ['pages', 'home', 'woman'],
      queryFn: async () => await getHomeData('women'),
    }),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <HomeNavbar />
      <DragScrollContainer className="flex-1 overflow-y-scroll main-page z-100" id="home-content">
        <HomeTabs defaultTab={shopParam} />
      </DragScrollContainer>
      <NotificationCarousel />
    </HydrationBoundary>
  );
}
