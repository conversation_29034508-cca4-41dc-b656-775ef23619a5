'use client';

import { getQueryClient } from '@/get-query-client';
import { useApiMutation } from '@/hooks/use-api-mutation';
import { useApi } from '@/hooks/use-api';
import { ApiResponse } from '@/types/api';
import { useCrypto } from '@/hooks/use-crypto';
import { useGetHeaders } from '@/hooks/use-get-headers';
import { useLogout } from '@/hooks/use-logout';
import { useGlobalActionsStore } from '@/store/global-actions.store';

export const useSendOTP = () => {
  const { encrypt } = useCrypto();
  const headers = useGetHeaders();

  return useApiMutation<boolean, Error, { phoneNumber: string; sendViaWhatsapp?: boolean }>({
    mutationFn: async ({ phoneNumber, sendViaWhatsapp = false }) => {
      try {
        const body = {
          phone_number: phoneNumber,
          otp_type: 'AUTH',
          send_whatsapp: sendViaWhatsapp,
        };

        const encryptedBody = await encrypt(JSON.stringify(body), false);

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/verificationreq_otp`, {
          method: 'POST',
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
          body: encryptedBody,
        });

        if (!response.ok) {
          throw new Error(`Failed to send OTP: ${response.statusText}`);
        }

        return true;
      } catch (error) {
        console.error('Error sending OTP:', error);
        return false;
      }
    },
    showLoader: true,
  });
};

export const useVerifyOTP = () => {
  const { apiClient } = useApi();

  return useApiMutation<ApiResponse, Error, { phoneNumber: string; otp: string }>({
    mutationFn: async ({ phoneNumber, otp }) => {
      const body = {
        phone_number: phoneNumber,
        otp_type: 'AUTH',
        otp: otp,
      };

      return apiClient<ApiResponse>({
        path: '/verification/verify',
        method: 'POST',
        body,
      });
    },
    onMutate: () => {
      useGlobalActionsStore.getState().loader.show();
    },
    onSuccess: async () => {
      const queryClient = getQueryClient();
      queryClient.cancelQueries();
      try {
        // Only revalidate page-related queries when user logs in
        await queryClient.invalidateQueries({ queryKey: ['pages'] });
      } finally {
        useGlobalActionsStore.getState().loader.hide();
      }
    },
    onError: () => {
      useGlobalActionsStore.getState().loader.hide();
    },
  });
};

// Logout mutation
export const useLogoutRequest = () => {
  const { apiClient } = useApi();

  const { logout } = useLogout();

  return useApiMutation<ApiResponse, Error, void>({
    mutationFn: async () => {
      return apiClient<ApiResponse>({
        path: '/user/logout',
        method: 'GET',
      });
    },
    onSuccess: () => {
      logout();
    },
    showLoader: true,
  });
};
