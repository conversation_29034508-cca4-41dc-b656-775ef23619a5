'use client';

import { AddAddressProps } from '@/actions/location.actions';
import { getQueryClient } from '@/get-query-client';
import { useApiMutation } from '@/hooks/use-api-mutation';
import { useApi } from '@/hooks/use-api';
import { ApiError, ApiResponse } from '@/types/api';
import { useGlobalActionsStore } from '@/store/global-actions.store';
import { useGlobalDataStore } from '@/store/global-data.store';

interface GetLocationByPlaceIdResponse extends ApiResponse {
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

interface UpdateLocationVariables {
  coordinate?: {
    latitude: number;
    longitude: number;
  };
  address_id?: string;
}

/*
  Updates the current location of the user in the database.
*/
export const useUpdateDeliveryLocation = () => {
  const { apiClient } = useApi();

  return useApiMutation<ApiResponse, ApiError, UpdateLocationVariables>({
    mutationFn: async (variables: UpdateLocationVariables) => {
      const { coordinate, address_id } = variables;
      let addressData = {};
      let updt_loc_call = false;

      if (address_id) {
        addressData = [
          {
            id: address_id,
          },
        ];
        updt_loc_call = false;
      }

      if (coordinate) {
        addressData = [
          {
            location: {
              current_latitude: coordinate?.latitude,
              current_longitude: coordinate?.longitude,
            },
          },
        ];
        updt_loc_call = true;
      }

      if (!address_id && !coordinate) {
        addressData = [];
        updt_loc_call = true;
      }

      const body = {
        updt_loc_call: updt_loc_call,
        override_delivery: true,
        address_data: addressData,
      };

      return apiClient<ApiResponse>({
        path: '/user_address/',
        method: 'POST',
        body: body,
      });
    },
    onMutate: () => {
      useGlobalActionsStore.getState().loader.show();
    },
    onSuccess: async () => {
      const queryClient = getQueryClient();
      try {
        await queryClient.invalidateQueries({ queryKey: ['pages'] });
      } finally {
        useGlobalActionsStore.getState().loader.hide();
      }
    },
    onError: (error) => {
      console.error('Mutation error while updating location:', error.message, error.details);
      useGlobalActionsStore.getState().loader.hide();
    },
  });
};

/*
  Adds a new address to the user's address list.
  If the address already exists, and its id is provided, it updates the address.
*/
export const useAddAddress = () => {
  const { apiClient } = useApi();

  return useApiMutation<ApiResponse, ApiError, AddAddressProps>({
    mutationFn: async (addressData: AddAddressProps) => {
      if (addressData.id === 'new_address') {
        delete addressData.id; // Remove the id property from the object
      }

      const body = {
        updt_loc_call: false,
        address_data: [addressData],
      };

      return apiClient<ApiResponse>({
        path: '/user_address/',
        method: 'POST',
        body: body,
      });
    },
    onMutate: () => {
      useGlobalActionsStore.getState().loader.show();
    },
    onSuccess: async () => {
      useGlobalActionsStore.getState().locationSheet.hide();
      const queryClient = getQueryClient();
      try {
        await queryClient.invalidateQueries({ queryKey: ['pages'] });
      } finally {
        useGlobalActionsStore.getState().loader.hide();
        // Scroll the main page container to top smoothly
        const mainPageElement = document.querySelector('.main-page');
        if (mainPageElement) {
          mainPageElement.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
        }
      }
    },
    onError: () => {
      useGlobalActionsStore.getState().loader.hide();
    },
  });
};

export const useDeleteAddress = () => {
  const { apiClient } = useApi();
  const { loader } = useGlobalActionsStore();
  const { globalData, setGlobalData } = useGlobalDataStore();

  return useApiMutation<ApiResponse, ApiError, string>({
    mutationFn: async (addressId: string) => {
      const body = {
        updt_loc_call: false,
        address_data: [
          {
            id: addressId,
            inactive: true,
          },
        ],
      };

      return apiClient<ApiResponse>({
        path: '/user_address/',
        method: 'POST',
        body: body,
      });
    },
    onMutate: (addressId: string) => {
      loader.show();
      const addresses = globalData.addresses.filter((address) => address.id !== addressId);
      setGlobalData({ ...globalData, addresses: addresses });
    },
    onSuccess: async () => {
      const queryClient = getQueryClient();
      try {
        await queryClient.invalidateQueries({ queryKey: ['pages'] });
      } finally {
        loader.hide();
      }
    },
    onError: () => {
      loader.hide();
    },
  });
};

export const useGetLocationByPlaceId = () => {
  const { apiClient } = useApi();

  return useApiMutation<GetLocationByPlaceIdResponse, ApiError, string>({
    mutationFn: async (placeId: string) => {
      const queryParams = new URLSearchParams({
        place_id: placeId,
        session_token: '6b752a98-7088-4432-b4de-9a96725c914a',
      });

      return apiClient<GetLocationByPlaceIdResponse>({
        path: `/user_address/details?${queryParams}`,
        method: 'GET',
      });
    },
  });
};
