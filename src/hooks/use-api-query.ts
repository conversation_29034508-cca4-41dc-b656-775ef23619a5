import { useQuery, useQ<PERSON>y<PERSON>lient, UseQueryOptions } from '@tanstack/react-query';
import { useEffect, useRef } from 'react';
import { toast } from '@/components/ui/sonner';
import { ApiResponse, ApiError } from '@/types/api';
import { useGlobalDataStore } from '@/store/global-data.store';
import { setCookie } from 'cookies-next';
import { useLogout } from './use-logout';
import { useGlobalActionsStore } from '@/store/global-actions.store';

type ApiQueryOptions<TData, TError, TQueryFnData = TData> = Omit<
  UseQueryOptions<TQueryFnData, TError, TData>,
  'onSuccess' | 'onError'
> & {
  onSuccess?: (data: TData) => void;
  onError?: (error: TError) => void;
  successMessage?: string;
  showErrorToast?: boolean; // Keep for backward compatibility but ignore in implementation
  invalidateOnSuccess?: (string | string[])[];
};

// Custom retry function with exponential backoff for server errors (>= 500)
const customRetry = (failureCount: number, error: unknown): boolean => {
  // Only retry for server errors (status >= 500)
  if (error instanceof Error && error.name === 'ApiError') {
    const apiError = error as unknown as ApiError;
    if (apiError.status >= 500 && failureCount < 3) {
      return true;
    }
  }
  return false;
};

// Custom retry delay with exponential backoff: 1s, 2s, 3s
const customRetryDelay = (attemptIndex: number): number => {
  return (attemptIndex + 1) * 1000; // 1000ms, 2000ms, 3000ms
};

export const useApiQuery = <
  TData = unknown,
  TError extends Error = ApiError,
  TQueryFnData = TData,
>({
  onSuccess,
  onError,
  successMessage,
  invalidateOnSuccess,
  showErrorToast = true,
  ...options
}: ApiQueryOptions<TData, TError, TQueryFnData>) => {
  const queryClient = useQueryClient();
  const { upsertGlobalData } = useGlobalDataStore();
  const { logout } = useLogout();
  // to track previous states to avoid unnecessary effect calls
  const prevDataRef = useRef<TData | undefined>(undefined);
  const prevErrorRef = useRef<TError | null>(null);
  const hasShownSuccessRef = useRef(false);

  const query = useQuery({
    ...options,
    retry: customRetry,
    retryDelay: customRetryDelay,
  });

  const { data, error, isSuccess, isError, isFetching } = query;

  // Handle success state
  useEffect(() => {
    if (isSuccess && data && data !== prevDataRef.current && !isFetching) {
      const response = data as unknown as ApiResponse;
      // Only show toast if we haven't shown it for this data instance
      if (!hasShownSuccessRef.current || prevDataRef.current !== data) {
        const apiMessage = response?.toast?.message;

        if (apiMessage) {
          if (response?.toast?.type === 'success') {
            toast({
              title: apiMessage,
              type: 'success',
            });
          } else if (response?.toast?.type === 'error') {
            toast({
              title: apiMessage,
              type: 'error',
            });
          }
        } else if (successMessage) {
          toast({
            title: successMessage,
            type: 'success',
          });
        }

        hasShownSuccessRef.current = true;
      }

      // Handle token updates
      if (response?.tokens) {
        if (response.tokens.session_token && response.tokens.session_token !== 'null') {
          setCookie('x-session-token', response.tokens.session_token, {
            secure: process.env.NODE_ENV === 'production',
            maxAge: 59 * 59 * 24 * 1,
            path: '/',
          });
          localStorage.setItem('flutter.accessToken', `"${response.tokens.session_token}"`);
        }
        if (response.tokens.refresh_token && response.tokens.refresh_token !== 'null') {
          setCookie('x-refresh-token', response.tokens.refresh_token, {
            secure: process.env.NODE_ENV === 'production',
            maxAge: 30 * 24 * 60 * 60,
            path: '/',
          });
          localStorage.setItem('flutter.refreshToken', `"${response.tokens.refresh_token}"`);
        }
      }

      // Handle global state updates
      if (response?.global_state) {
        const { timestamps } = useGlobalDataStore.getState();
        if (!timestamps[response.timestamp]) {
          upsertGlobalData(response.global_state, response.timestamp);
        }
      }

      if (response?.default_actions) {
        response.default_actions.forEach((action) => {
          if (action.type === 'full_screen_banner') {
            useGlobalActionsStore.getState().fullScreenBanner.show(action);
          } else if (action.type === 'generic_sheet') {
            useGlobalActionsStore.getState().genericSheet.show(action);
          } else if (action.type === 'signup_sheet') {
            useGlobalActionsStore.getState().welcomeSheet.show(action);
          } else if (action.type === 'update') {
            useGlobalActionsStore.getState().update.show(action);
          }
        });
      }

      // Invalidate queries if specified
      if (invalidateOnSuccess) {
        invalidateOnSuccess.forEach((key) => {
          queryClient.invalidateQueries({ queryKey: Array.isArray(key) ? key : [key] });
        });
      }

      const isUnderMaintenance = useGlobalActionsStore.getState().maintenanceScreen.isVisible;
      if (isUnderMaintenance) {
        useGlobalActionsStore.getState().maintenanceScreen.hide();
        queryClient.invalidateQueries();
      }

      // Call custom onSuccess callback
      onSuccess?.(data);

      prevDataRef.current = data;
    }
  }, [
    isSuccess,
    data,
    isFetching,
    successMessage,
    invalidateOnSuccess,
    onSuccess,
    queryClient,
    upsertGlobalData,
  ]);

  // Handle error state
  useEffect(() => {
    if (isError && error && error !== prevErrorRef.current && !isFetching) {
      if (error.name === 'ApiError') {
        const errorStatus = parseInt(error.message.split('-')[0].split(':')[1]) as number;
        const errorMessage = error.message.split('-')[1];

        if (errorStatus === 401) {
          logout();
        } else if (errorStatus >= 500) {
          // show maintenance screen
          useGlobalActionsStore.getState().maintenanceScreen.show();
        } else {
          onError?.(error);
          if (showErrorToast) {
            toast({
              title: errorMessage,
              type: 'error',
            });
          }
        }
      } else {
        onError?.(error);
        // will trigger the unexpected error screen
        throw new Error('An unexpected error occurred.');
      }

      prevErrorRef.current = error;
    }
  }, [isError, error, isFetching, onError, logout, showErrorToast]);

  // Reset success flag when query starts fetching again
  useEffect(() => {
    if (isFetching) {
      hasShownSuccessRef.current = false;
    }
  }, [isFetching]);

  return query;
};
