/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useCallback } from 'react';
import { useCrypto } from './use-crypto';
import { ApiError, ApiClientOptions, ApiResponse } from '@/types/api';
import { formatData } from '@/lib/formatters/data.formatter';
import { logger } from '@/utils/logger';
import { getCookie } from 'cookies-next/client';

export const useApi = (): {
  apiClient: <T extends ApiResponse>(
    options: ApiClientOptions & { _isRetryAttempt?: boolean }
  ) => Promise<T>;
} => {
  const { encrypt, decrypt } = useCrypto();

  const apiClient = useCallback(
    async <T>({
      method = 'GET',
      path,
      body,
      queryParams,
      bearerToken,
      _isRetryAttempt = false,
    }: ApiClientOptions & { _isRetryAttempt?: boolean }): Promise<T> => {
      try {
        logger.dev('useApi called');
        logger.dev('path: ', path);
        logger.dev('body: ', body);
        logger.dev('queryParams: ', queryParams);
        logger.dev('attempt: ', _isRetryAttempt);

        // make headers - get fresh headers on every call to ensure updated cookies
        const deviceId = getCookie('X-Device-Id');
        const sessionToken = getCookie('x-session-token');
        const xPresentLat = getCookie('X-Present-Lat');
        const xPresentLong = getCookie('X-Present-Long');
        const addressId = getCookie('X-Address-Id');
        const currentTime = new Date().toISOString();

        const requestHeaders: Record<string, string> = {
          'Content-Type': 'application/json',
          'X-Device-Id': deviceId || '',
          'X-Current-Time': currentTime,
          operatingSystem: 'web',
          'Ngrok-Skip-Browser-Warning': 'true',
        };

        if (sessionToken) {
          requestHeaders['Authorization'] = `Bearer ${sessionToken}`;
        }

        if (xPresentLat && xPresentLong) {
          requestHeaders['X-Present-Lat'] = xPresentLat;
          requestHeaders['X-Present-Long'] = xPresentLong;
        }

        if (addressId) {
          requestHeaders['X-Address-Id'] = addressId;
        }

        logger.dev('headers: ', requestHeaders);

        // override the bearer token if provided
        if (bearerToken) {
          requestHeaders['Authorization'] = `Bearer ${bearerToken}`;
        }

        const url = new URL(`${process.env.NEXT_PUBLIC_API_URL}${path}`);
        logger.dev('url: ', url.toString());

        // make query params
        if (queryParams) {
          Object.entries(queryParams).forEach(([key, value]) => {
            url.searchParams.append(key, value);
          });
        }

        let requestBody = body;

        if (body) {
          try {
            requestBody = await encrypt(JSON.stringify(body), false);
          } catch (error) {
            logger.error('Error while encrypting', error);
            throw new Error('Something went wrong');
          }
        }

        const response = await fetch(url.toString(), {
          method,
          headers: requestHeaders,
          body: requestBody,
        });

        if (!response.ok) {
          const errorData = await response.json();
          if (response.status === 401) {
            // Prevent infinite recursion by checking if this is already a retry attempt
            if (_isRetryAttempt) {
              logger.devError('useApi: Infinite retry attempt detected');
              throw new ApiError('Unauthorized', 401);
            }

            const refreshToken = getCookie('x-refresh-token');
            if (refreshToken === 'null' || !refreshToken) {
              logger.devError('useApi: No refresh token found');
              throw new ApiError('Unauthorized', 401);
            } else {
              try {
                const refreshTokenResponse = await apiClient<RefreshTokenResponse>({
                  method: 'GET',
                  path: '/verificationrefresh',
                  bearerToken: refreshToken,
                  _isRetryAttempt: true, // Mark this as a retry attempt
                });
                logger.dev('refreshTokenResponse: ', refreshTokenResponse);
                const response = await apiClient<T>({
                  method: method,
                  path: path,
                  bearerToken: refreshTokenResponse.access_token,
                  queryParams: queryParams,
                  body: body,
                  _isRetryAttempt: true, // Mark this as a retry attempt
                });
                return {
                  ...response,
                  tokens: {
                    session_token: refreshTokenResponse.access_token,
                    refresh_token: refreshTokenResponse.refresh_token,
                  },
                } as T;
              } catch (error) {
                logger.devError('useApi: Failed to refresh token', error);
                throw new ApiError('Unauthorized', 401);
              }
            }
          } else {
            const decryptedErrorData: any = await decrypt(errorData);
            logger.dev('decryptedErrorData: ', decryptedErrorData);
            if (decryptedErrorData.message) {
              throw new ApiError(
                decryptedErrorData.message,
                decryptedErrorData.statusCode,
                decryptedErrorData
              );
            } else {
              logger.devError('useApi: No error message found in decrypted error data');
              throw new Error('Something went wrong');
            }
          }
        }

        const sessionTokenResponse =
          response.headers.get('x-access-token') || response.headers.get('x-session-token');
        const refreshTokenResponse = response.headers.get('x-refresh-token');

        // getting toast if available in headers
        const toast = {
          message: response.headers.get('custom_snackbar_msg'),
          icon: response.headers.get('snack_bar_icon_path'),
          type: 'success',
        };

        if (toast.message) {
          logger.dev('toast: ', toast);
        }

        const data = await response.json();

        try {
          const decryptedData: any = await decrypt(data);
          const formattedData = decryptedData ? formatData(decryptedData) : null;

          return {
            ...decryptedData,
            tokens: {
              session_token: sessionTokenResponse,
              refresh_token: refreshTokenResponse,
            },
            toast,
            global_state: formattedData?.global_app_data,
            default_actions: formattedData?.default_actions,
            timestamp: Math.floor(Date.now() / 1000),
          } as T;
        } catch (error) {
          logger.devError('Error while formatting', error);
          throw new Error('Something went wrong');
        }
      } catch (error) {
        if (error instanceof ApiError) {
          logger.apiError(path, error);
          throw error;
        } else {
          logger.devError('Unexpected error at path', path, error);
          throw new Error('Something went wrong');
        }
      }
    },
    [encrypt, decrypt]
  );

  return { apiClient };
};

interface RefreshTokenResponse extends ApiResponse {
  access_token: string;
  refresh_token: string;
}
