import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';
import { toast } from '@/components/ui/sonner';
import { ApiResponse } from '@/types/api';
import { useGlobalDataStore } from '@/store/global-data.store';
import { setCookie } from 'cookies-next';
import { useLogout } from './use-logout';
import { useGlobalActionsStore } from '@/store/global-actions.store';

type ApiMutationOptions<TData, TError, TVariables, TContext> = UseMutationOptions<
  TData,
  TError,
  TVariables,
  TContext
> & {
  showLoader?: boolean;
  successMessage?: string;
  invalidateOnSuccess?: (string | string[])[];
  showToastOnError?: boolean;
};

export const useApiMutation = <TData, TError extends Error, TVariables = void, TContext = unknown>({
  onSuccess,
  onError,
  onMutate,
  successMessage,
  invalidateOnSuccess,
  showLoader = false,
  showToastOnError = true,
  ...options
}: ApiMutationOptions<TData, TError, TVariables, TContext>) => {
  const queryClient = useQueryClient();
  const { upsertGlobalData } = useGlobalDataStore();
  const { logout } = useLogout();

  return useMutation({
    ...options,
    onSuccess: (data, variables, context) => {
      const response = data as ApiResponse;
      const apiMessage = response?.toast?.message;

      if (apiMessage) {
        if (response?.toast?.type === 'success') {
          toast({
            title: apiMessage,
            type: 'success',
            icon: response?.toast?.icon || undefined,
          });
        } else {
          toast({
            title: apiMessage,
            type: 'error',
            icon: response?.toast?.icon || undefined,
          });
        }
      } else if (successMessage) {
        toast({
          title: successMessage,
          type: 'success',
          icon: response?.toast?.icon || undefined,
        });
      }

      if (response?.tokens) {
        if (response.tokens.session_token) {
          setCookie('x-session-token', response.tokens.session_token, {
            secure: process.env.NODE_ENV === 'production',
            maxAge: 59 * 59 * 24 * 1,
            path: '/',
          });
          localStorage.setItem('flutter.accessToken', `"${response.tokens.session_token}"`);
          localStorage.setItem('accessToken', `"${response.tokens.session_token}"`);
        }
        if (response.tokens.refresh_token) {
          setCookie('x-refresh-token', response.tokens.refresh_token, {
            secure: process.env.NODE_ENV === 'production',
            maxAge: 30 * 24 * 60 * 60,
            path: '/',
          });
          localStorage.setItem('flutter.refreshToken', `"${response.tokens.refresh_token}"`);
        }
      }

      if (response?.global_state) {
        const { timestamps } = useGlobalDataStore.getState();
        if (!timestamps[response.timestamp]) {
          upsertGlobalData(response.global_state, response.timestamp);
        }
      }

      if (response?.default_actions) {
        response.default_actions.forEach((action) => {
          if (action.type === 'full_screen_banner') {
            useGlobalActionsStore.getState().fullScreenBanner.show(action);
          } else if (action.type === 'generic_sheet') {
            useGlobalActionsStore.getState().genericSheet.show(action);
          } else if (action.type === 'signup_sheet') {
            useGlobalActionsStore.getState().welcomeSheet.show(action);
          } else if (action.type === 'update') {
            useGlobalActionsStore.getState().update.show(action);
          }
        });
      }

      if (invalidateOnSuccess) {
        invalidateOnSuccess.forEach((key) => {
          queryClient.invalidateQueries({ queryKey: Array.isArray(key) ? key : [key] });
        });
      }

      const isUnderMaintenance = useGlobalActionsStore.getState().maintenanceScreen.isVisible;
      if (isUnderMaintenance) {
        useGlobalActionsStore.getState().maintenanceScreen.hide();
        queryClient.invalidateQueries();
      }

      onSuccess?.(data, variables, context as TContext);
    },
    onError: (error, variables, context) => {
      if (error.name === 'ApiError') {
        const errorStatus = parseInt(error.message.split('-')[0].split(':')[1]) as number;
        const errorMessage = error.message.split('-')[1];

        if (errorStatus === 401) {
          logout();
        } else if (errorStatus >= 500) {
          // show maintenance screen
          useGlobalActionsStore.getState().maintenanceScreen.show();
        } else {
          if (showToastOnError) {
            toast({
              title: errorMessage,
              type: 'error',
            });
          }
          onError?.(error, variables, context as TContext);
        }
      } else {
        onError?.(error, variables, context as TContext);
      }
    },
    onSettled: () => {
      if (showLoader) {
        useGlobalActionsStore.getState().loader.hide();
      }
    },
    onMutate: (variables) => {
      if (showLoader) {
        useGlobalActionsStore.getState().loader.show();
      }
      return onMutate?.(variables);
    },
  });
};
