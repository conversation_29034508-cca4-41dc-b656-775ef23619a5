import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>etHeader, Sheet<PERSON><PERSON><PERSON> } from '../ui/sheet';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON> } from '../ui/button';
import { MapPinIcon, XIcon, ArrowLeft } from 'lucide-react';
import { Form, FormControl, FormField, FormItem } from '../ui/form';
import { Input } from '../ui/input';
import React, { useState } from 'react';
import { AddAddressProps, GetInitialCoordinatesProps } from '@/actions/location.actions';
import { useAddAddress } from '@/mutations/location.mutations';
import { useGlobalDataStore } from '@/store/global-data.store';
import { toast } from '@/components/ui/sonner';
import { useRouter } from 'next/navigation';
import OfficeIcon from '../icons/office-icon';
import HomeAddressIcon from '../icons/home-address';

const addressFormSchema = z
  .object({
    house_detail: z.string().min(1, { message: 'House number is required' }),
    floor: z.string().optional(),
    reciever_name: z.string().min(1, { message: 'Reciever name is required' }),
    reciever_phone: z
      .string()
      .min(1, { message: 'Please enter your phone number' })
      .min(10, { message: 'Phone number must be at least 10 digits' })
      .regex(/^\d+$/, { message: 'Phone number must contain only digits' }),
    address_type: z.enum(['home', 'office', 'other'], {
      required_error: 'Address type is required',
    }),
    other_address_type: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.address_type === 'other') {
        return !!data.other_address_type && data.other_address_type.trim().length > 0;
      }
      return true;
    },
    {
      message: 'Please specify the address type',
      path: ['other_address_type'],
    }
  );

export const AddressForm = ({
  isOpen,
  onOpenChange,
  address,
  coordinates,
  addressData,
  onLocationUpdate,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  addressData: GetInitialCoordinatesProps | undefined;
  onLocationUpdate?: () => void;
}) => {
  const { mutateAsync: addAddress, isPending } = useAddAddress();
  const { globalData } = useGlobalDataStore();
  const [showOtherInput, setShowOtherInput] = useState(addressData?.other_type_name ? true : false);
  const router = useRouter();
  const user = globalData.user;

  const form = useForm<z.infer<typeof addressFormSchema>>({
    resolver: zodResolver(addressFormSchema),
    defaultValues: {
      house_detail: addressData?.house_number || '',
      floor: addressData?.apartment || '',
      reciever_name: addressData?.name || user?.full_name || '',
      reciever_phone: addressData?.phone || user?.phone_number || '',
      address_type: (addressData?.address_type as 'home' | 'office' | 'other') || 'home',
      other_address_type: addressData?.other_type_name || '',
    },
  });

  const onSubmit = async (data: z.infer<typeof addressFormSchema>) => {
    const addressDataBody: AddAddressProps = {
      tag: data.other_address_type || data.address_type,
      receiver_name: data.reciever_name,
      receiver_phone_number: data.reciever_phone,
      google_address: address,
      location: {
        current_latitude: coordinates.latitude,
        current_longitude: coordinates.longitude,
      },
      location_data: data.floor,
      residence_number: data.house_detail,
    };
    if (addressData?.id) {
      addressDataBody.id = addressData.id;
    }
    await addAddress(addressDataBody);
    onLocationUpdate?.();
    onOpenChange(false);
    router.back();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = await form.trigger();
    if (!result) {
      const firstError = Object.values(form.formState.errors)[0];
      if (firstError?.message) {
        toast({
          title: firstError.message,
          type: 'error',
        });
      }
      return;
    }
    form.handleSubmit(onSubmit)(e);
  };

  const handleBackToSelectors = () => {
    setShowOtherInput(false);
    form.resetField('address_type');
    form.setValue('other_address_type', '');
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="px-0 z-75">
        <SheetHeader>
          <SheetClose asChild>
            <button className="absolute top-12 left-12">
              <ArrowLeft className="size-20" />
            </button>
          </SheetClose>
          <SheetTitle className="text-content-primary text-center text-md py-8">
            Enter Address Details
          </SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form onSubmit={handleSubmit} className="mt-10 ">
            <div className="max-h-[55dvh] overflow-y-scroll">
              <div className="flex justify-between items-center px-16 py-0">
                <p className="font-semibold text-sm">{address}</p>
                <button
                  className="text-content-theme bg-surface-dead-1 cursor-pointer px-8 py-4 rounded-sm text-sm"
                  type="button"
                  onClick={() => {
                    onOpenChange(false);
                  }}
                >
                  Change
                </button>
              </div>
              <div className="py-8 flex flex-col gap-12 px-14">
                <FormField
                  control={form.control}
                  name="house_detail"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          placeholder="House no./ Flat / Building name*"
                          {...field}
                          className="w-full"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="floor"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input placeholder="Floor (optional)" {...field} className="w-full" />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <p className="text-sm font-semibold pt-2 text-neutral-300">Receiver Details</p>
                <FormField
                  control={form.control}
                  name="reciever_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input placeholder="Name*" {...field} className="w-full" />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="reciever_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          className="w-full"
                          type="tel"
                          placeholder="Phone Number*"
                          pattern="[0-9]*"
                          maxLength={10}
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            const numericValue = value.replace(/[^0-9]/g, '');
                            const truncatedValue = numericValue.slice(0, 10);
                            field.onChange(truncatedValue);
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <p className="text-sm font-semibold pt-2 text-neutral-300">Save address as</p>
                <FormField
                  control={form.control}
                  name="address_type"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormControl>
                        {!showOtherInput ? (
                          <div className="flex space-x-12">
                            <Button
                              type="button"
                              variant={field.value === 'home' ? 'default' : 'secondary'}
                              onClick={() => {
                                field.onChange('home');
                                form.setValue('other_address_type', '');
                              }}
                              className="py-16 text-xs font-normal"
                            >
                              <HomeAddressIcon className="size-16 mr-2" /> Home
                            </Button>
                            <Button
                              type="button"
                              variant={field.value === 'office' ? 'default' : 'secondary'}
                              onClick={() => {
                                field.onChange('office');
                                form.setValue('other_address_type', '');
                              }}
                              className="py-16 text-xs font-normal"
                            >
                              <OfficeIcon className="mr-2 size-16" /> Office
                            </Button>
                            <Button
                              type="button"
                              variant="secondary"
                              onClick={() => {
                                field.onChange('other');
                                setShowOtherInput(true);
                              }}
                              className="py-16 text-xs font-normal"
                            >
                              <MapPinIcon className="mr-2 size-12" /> Other
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <FormField
                              control={form.control}
                              name="other_address_type"
                              render={({ field: otherField }) => (
                                <div className="flex-1 relative">
                                  <Input
                                    placeholder="Specify address type (e.g., Gym, Friend's place)"
                                    {...otherField}
                                    autoFocus
                                  />
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleBackToSelectors}
                                    className="absolute right-8 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-neutral-700 rounded-full cursor-pointer"
                                  >
                                    <XIcon className="size-12" />
                                  </Button>
                                </div>
                              )}
                            />
                          </div>
                        )}
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="pt-14 mt-18 pb-18 border-t px-14 rounded-t-lg">
              <Button
                type="submit"
                className={`w-full py-22 ${!form.formState.isValid ? 'bg-surface-dead-1 hover:bg-surface-dead-1' : ''}`}
                disabled={isPending}
                onClick={async (e) => {
                  if (!form.formState.isValid) {
                    e.preventDefault();
                    const result = await form.trigger();
                    if (!result) {
                      const firstError = Object.values(form.formState.errors)[0];
                      if (firstError?.message) {
                        toast({
                          title: firstError.message,
                          type: 'error',
                        });
                      }
                    }
                  }
                }}
              >
                Confirm Location
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};
