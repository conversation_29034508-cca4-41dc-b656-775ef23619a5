'use client';

import * as Tabs from '@radix-ui/react-tabs';
import TabContent from './tab-content';
import CircularGradient from '../common/gradient';
import { useBottomBarVisibility } from '@/hooks/use-bottom-bar-visibility';
import { motion, AnimatePresence } from 'motion/react';
import { useInitialHomepageData } from '@/queries/home.queries';
import HomeTabButton from './home-tab-button';
import { useThemeColorWithUrlSupport } from '@/store/theme';
import { useTagWithUrlSupport } from '@/store/tag';

export default function HomeTabs({ defaultTab }: { defaultTab: string | undefined }) {
  const isVisible = useBottomBarVisibility();
  const { data } = useInitialHomepageData();
  const { tag } = useTagWithUrlSupport();
  const { currentTheme } = useThemeColorWithUrlSupport({
    defaultTag: data?.global_state?.default_tag as 'man' | 'woman' | undefined,
  });

  const backgroundTransition = {
    type: 'spring',
    stiffness: 300,
    damping: 30,
    mass: 0.8,
  };

  // Use the current tag from the store, which respects the priority order
  const currentTab = tag || defaultTab || data?.global_state?.default_tag || 'man';

  return (
    <>
      <Tabs.Root value={currentTab} className="w-full">
        <Tabs.List>
          <div className="fixed w-[calc(100%+0.5px)] overflow-hidden top-[calc(var(--scale)*calc(52+var(--top-banner-height)+var(--top-update-height)))] h-[calc(var(--scale)*110)] z-20 pointer-events-none">
            {/* Gradient positioned within container */}
            <div className="absolute pointer-events-none top-[calc(var(--scale)*-55)] md:height-[calc(var(--scale)*55)] left-0 right-0 z-10">
              <CircularGradient target="#home-content" themeColor={currentTheme} />
            </div>

            <AnimatePresence>
              {isVisible && (
                <>
                  {/* Background Layer */}
                  <motion.div
                    key="background"
                    initial={{ y: 0 }}
                    exit={{ y: 'calc(var(--scale) * -120)' }}
                    transition={backgroundTransition}
                    className="bg-surface-primary w-full h-64"
                    style={{ position: 'relative', zIndex: 8 }}
                  />

                  {/* Buttons Layer */}
                  <motion.div
                    key="tag-buttons"
                    initial={{ y: 0 }}
                    exit={{ y: 'calc(var(--scale) * -120)' }}
                    transition={{
                      ...backgroundTransition,
                    }}
                    className="flex w-full justify-center bg-transparent absolute top-0 pointer-events-auto"
                    style={{ zIndex: 10 }}
                    id="home-tabs-buttons"
                  >
                    <HomeTabButton tag="man" />
                    <HomeTabButton tag="woman" />
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </Tabs.List>

        <div className="z-10 pt-72">
          <Tabs.Content value="man" className="w-full data-[state=inactive]:hidden" forceMount>
            <TabContent type="man" />
          </Tabs.Content>
          <Tabs.Content value="woman" className="w-full data-[state=inactive]:hidden" forceMount>
            <TabContent type="woman" />
          </Tabs.Content>
        </div>
      </Tabs.Root>
    </>
  );
}
