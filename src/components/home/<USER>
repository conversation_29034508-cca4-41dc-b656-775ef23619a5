'use client';

import SearchOutline from '../icons/search-outline';
import HeartOutline from '../icons/heart-outline';
import ProfileOutline from '../icons/profile-outline';
import Update from '../common/update';
import Link from 'next/link';
import CircularGradient from '../common/gradient';
import { useGlobalDataStore } from '@/store/global-data.store';
import { useInitialHomepageData } from '@/queries/home.queries';
import { useThemeColorWithUrlSupport } from '@/store/theme';
import { useGetInitialLocationData } from '@/queries/location.queries';
import { useGlobalActionsStore } from '@/store/global-actions.store';
import { ChevronDownIcon } from 'lucide-react';

export default function HomeNavbar() {
  const { globalData } = useGlobalDataStore();
  const { data } = useInitialHomepageData();
  const defaultTag = data?.global_state?.default_tag || 'man';

  const { currentTheme } = useThemeColorWithUrlSupport({
    defaultTag: defaultTag as 'man' | 'woman' | undefined,
  });
  const { data: initialLocationData } = useGetInitialLocationData();
  const { locationSheet } = useGlobalActionsStore();

  const address =
    globalData.current_address?.display_string ||
    globalData.current_address?.address ||
    initialLocationData?.global_state?.current_address?.display_string ||
    initialLocationData?.global_state?.current_address?.address;

  const addressType =
    globalData.current_address?.other_type_name ||
    (globalData.current_address?.address_type !== 'other' &&
      globalData.current_address?.address_type) ||
    initialLocationData?.global_state?.current_address?.other_type_name ||
    (initialLocationData?.global_state?.current_address?.address_type !== 'other' &&
      initialLocationData?.global_state?.current_address?.address_type);

  return (
    <div className="" id="home-navbar">
      <div
        className="w-full h-52 flex justify-between items-center px-16 z-1000"
        style={{
          lineHeight: '20 * var(--scale)',
        }}
      >
        <div></div>
        {globalData.current_address.address && (
          <div className="relative z-10 w-full cursor-pointer" onClick={locationSheet.show}>
            <p className="text-lg font-black tracking-[-0.06em]">
              Delivery in{' '}
              <span style={{ color: currentTheme }}>
                {globalData.current_address.delivery_time}
              </span>
            </p>
            <div
              className="w-225 p-0 text-left text-xs font-bold flex items-center gap-2 cursor-pointer"
              onClick={locationSheet.show}
            >
              <div className="flex items-center gap-2 w-full overflow-hidden">
                {addressType && (
                  <p className="font-semibold whitespace-nowrap flex-shrink-0">
                    {addressType.charAt(0).toUpperCase() + addressType.slice(1)} -
                  </p>
                )}
                <span
                  className={`text-surface-static-quinary font-light text-[calc(14*var(--scale))] truncate overflow-hidden whitespace-nowrap`}
                >
                  {address}
                </span>
              </div>
              <ChevronDownIcon className="w-16 h-16" />
            </div>
          </div>
        )}
        <div className="flex gap-16 z-50">
          <Link href="/search">
            <SearchOutline className="size-24" fill="#FAFAFA" />
          </Link>
          <Link href="/collections/wishlist">
            <HeartOutline className="size-24" fill="#FAFAFA" />
          </Link>
          <Link href="/profile_page">
            <ProfileOutline className="size-24 cursor-pointer" fill="#FAFAFA" />
          </Link>
        </div>
        <div
          className="absolute pointer-events-none overflow-clip"
          style={{
            top: '0', // Offset to account for container position
            left: '0',
            right: '0',
            height: 'calc(var(--scale) * 52)',
            zIndex: 0, // Below buttons within container
          }}
        >
          <CircularGradient target="#home-content" themeColor={currentTheme} />
        </div>
      </div>
      <Update />
    </div>
  );
}
