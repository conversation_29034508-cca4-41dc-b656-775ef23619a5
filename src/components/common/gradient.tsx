'use client';

import { useState } from 'react';
import { useScroll } from '@/hooks/use-scroll';

export default function CircularGradient({
  target,
  themeColor,
}: {
  target: string;
  themeColor: string;
}) {
  const [opacity, setOpacity] = useState(0.5);

  const handleScroll = (element: HTMLElement) => {
    const scrollY = element.scrollTop;
    const maxScroll = 100;
    const newOpacity = scrollY ? Math.max(0, 0.5 - (scrollY * 0.5) / maxScroll) : 0.5;
    setOpacity(newOpacity);
  };

  useScroll(target, handleScroll);

  return (
    <div
      className="absolute top-0 -left-15 -right-15 h-175 pointer-events-none"
      style={{
        background: `radial-gradient(ellipse at 50% 0%, ${themeColor} 0%, transparent 70%)`,
        opacity: opacity,
        zIndex: 25,
      }}
      aria-hidden="true"
    />
  );
}
