'use client';

import BottomNavItemClient from './bottom-nav-item-client';
import PicksIcon from '../icons/picks-icon';
import TrendsOutline from '../icons/trends';
import BagIcon from '../icons/bag-icon';
import HomeIcon from '../icons/home-icon';
import CollectionIcon from '../icons/collection-icon';
import { AnimatePresence, motion } from 'motion/react';
import { useBottomBarVisibility } from '@/hooks/use-bottom-bar-visibility';
import { useGlobalDataStore } from '@/store/global-data.store';
import { usePathname } from 'next/navigation';
import { useTagWithUrlSupport } from '@/store/tag';
import { getCookie } from 'cookies-next/client';
import { useEffect, useState } from 'react';

export function BottomNavbar() {
  const pathname = usePathname();
  const selected = pathname.split('/')[1];
  const isVisible = useBottomBarVisibility();
  const { globalData } = useGlobalDataStore();
  const { tag, userPreference } = useTagWithUrlSupport();

  // Track brands selection state for collection navigation
  const [isBrandSelected, setIsBrandSelected] = useState(false);

  useEffect(() => {
    // Only read brands cookie on client side
    const brandCookie = getCookie('X-Is-Brand-Selected');
    setIsBrandSelected(brandCookie === 'true');
  }, [tag]); // Re-read when tag changes

  // Simplified navigation logic
  const getNavigationTag = (currentTag: string) => {
    // For brands, use the user's preference for non-collection pages
    return currentTag === 'brands' ? userPreference : currentTag;
  };

  const navigationTag = getNavigationTag(tag);

  // For collection navigation, use brands if selected, otherwise use current tag
  const collectionTag = isBrandSelected ? 'brands' : tag;

  const navItems = [
    {
      IconComponent: HomeIcon,
      label: 'Home',
      href: `/?shop=${navigationTag}`,
      key: '',
      supportsFilledState: true,
    },
    {
      IconComponent: globalData.is_discover_live ? PicksIcon : null,
      label: 'Picks',
      href: '/picks',
      key: 'picks',
      supportsFilledState: true,
    },
    {
      IconComponent: CollectionIcon,
      label: 'Collection',
      href: `/collection?shop=${collectionTag}`, // Use collection-specific tag (preserves brands selection)
      key: 'collection',
      supportsFilledState: false,
    },
    {
      IconComponent: TrendsOutline,
      label: 'Trends',
      href: `/trends?shop=${navigationTag}`,
      key: 'trends',
      supportsFilledState: true,
    },
    {
      IconComponent: BagIcon,
      label: 'Bag',
      href: '/cart',
      key: 'bag',
      supportsFilledState: false,
      badge: globalData.cart_items_count || null,
    },
  ];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.nav
          initial={{ y: '100%' }}
          animate={{ y: 0 }}
          exit={{ y: '100%' }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
            mass: 0.8,
          }}
          className="pb-12 fixed -bottom-5 left-0 w-[calc(var(--scale)*375.4)] bg-surface-primary z-500"
        >
          <div className="flex items-center justify-around">
            {navItems.map(
              (item) =>
                item.IconComponent && (
                  <BottomNavItemClient
                    key={item.key}
                    IconComponent={item.IconComponent}
                    label={item.label}
                    href={item.href}
                    isSelected={selected === item.key}
                    supportsFilledState={item.supportsFilledState}
                    badge={item.badge?.toString() || undefined}
                  />
                )
            )}
          </div>
        </motion.nav>
      )}
    </AnimatePresence>
  );
}
