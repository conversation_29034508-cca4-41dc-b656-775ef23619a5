'use client';

import BottomNavItemClient from './bottom-nav-item-client';
import PicksIcon from '../icons/picks-icon';
import TrendsOutline from '../icons/trends';
import BagIcon from '../icons/bag-icon';
import HomeIcon from '../icons/home-icon';
import CollectionIcon from '../icons/collection-icon';
import { AnimatePresence, motion } from 'motion/react';
import { useBottomBarVisibility } from '@/hooks/use-bottom-bar-visibility';
import { useGlobalDataStore } from '@/store/global-data.store';
import { usePathname } from 'next/navigation';
import { useTagWithUrlSupport } from '@/store/tag';
import { useAppConfigStore } from '@/store/app-config';
import { getCookie } from 'cookies-next/client';
import { useEffect, useState } from 'react';

export function BottomNavbar() {
  const pathname = usePathname();
  const selected = pathname.split('/')[1];
  const isVisible = useBottomBarVisibility();

  // Use state to handle client-side cookie reading to avoid hydration issues
  const [isBrandSelected, setIsBrandSelected] = useState(false);
  const [brandSelectedCookie, setBrandSelectedCookie] = useState<string | undefined>(undefined);

  useEffect(() => {
    // Only read cookie on client side after hydration
    const cookieValue = getCookie('X-Is-Brand-Selected');
    const brandSelected = cookieValue === 'true';
    setIsBrandSelected(brandSelected);
    setBrandSelectedCookie(cookieValue);

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Bottom Navbar - Client-side cookie read:', cookieValue, '→', brandSelected);
    }
  }, []);

  const { tag } = useTagWithUrlSupport();

  useEffect(() => {
    // Re-read cookie when URL tag changes
    const cookieValue = getCookie('X-Is-Brand-Selected');
    const brandSelected = cookieValue === 'true';
    setIsBrandSelected(brandSelected);
    setBrandSelectedCookie(cookieValue);

    if (process.env.NODE_ENV === 'development') {
      console.log(
        '🔍 Bottom Navbar - Cookie re-read on tag change:',
        cookieValue,
        '→',
        brandSelected,
        'tag:',
        tag
      );
    }
  }, [tag]);

  // Add a polling mechanism to check for cookie changes
  useEffect(() => {
    const interval = setInterval(() => {
      const currentCookieValue = getCookie('X-Is-Brand-Selected');
      if (currentCookieValue !== brandSelectedCookie) {
        const brandSelected = currentCookieValue === 'true';
        setIsBrandSelected(brandSelected);
        setBrandSelectedCookie(currentCookieValue);

        if (process.env.NODE_ENV === 'development') {
          console.log(
            '🔍 Bottom Navbar - Cookie change detected via polling:',
            brandSelectedCookie,
            '→',
            currentCookieValue,
            '→',
            brandSelected
          );
        }
      }
    }, 100); // Check every 100ms

    return () => clearInterval(interval);
  }, [brandSelectedCookie]);
  const { globalData } = useGlobalDataStore();
  const defaultTag = globalData.default_tag ?? 'man';
  const { config } = useAppConfigStore();
  const userPreference = config.userPreference || defaultTag;

  const collectionShop = isBrandSelected ? 'brands' : (tag ?? userPreference);

  // Debug logging - remove in production
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Bottom Navbar Debug:');
    console.log('  - brandSelectedCookie:', brandSelectedCookie);
    console.log('  - isBrandSelected:', isBrandSelected);
    console.log('  - tag:', tag);
    console.log('  - userPreference:', userPreference);
    console.log('  - collectionShop:', collectionShop);
    console.log('  - final collection href:', `/collection?shop=${collectionShop}`);
  }

  const navItems = [
    {
      IconComponent: HomeIcon,
      label: 'Home',
      href: `/?shop=${tag === 'brands' ? userPreference : (tag ?? userPreference)}`,
      key: '',
      supportsFilledState: true,
    },
    {
      IconComponent: globalData.is_discover_live ? PicksIcon : null,
      label: 'Picks',
      href: '/picks',
      key: 'picks',
      supportsFilledState: true,
    },
    {
      IconComponent: CollectionIcon,
      label: 'Collection',
      href: `/collection?shop=${collectionShop}`,
      key: 'collection',
      supportsFilledState: false,
    },
    {
      IconComponent: TrendsOutline,
      label: 'Trends',
      href: `/trends?shop=${tag === 'brands' ? userPreference : (tag ?? userPreference)}`,
      key: 'trends',
      supportsFilledState: true,
    },
    {
      IconComponent: BagIcon,
      label: 'Bag',
      href: '/cart',
      key: 'bag',
      supportsFilledState: false,
      badge: globalData.cart_items_count || null,
    },
  ];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.nav
          initial={{ y: '100%' }}
          animate={{ y: 0 }}
          exit={{ y: '100%' }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
            mass: 0.8,
          }}
          className="pb-12 fixed -bottom-5 left-0 w-[calc(var(--scale)*375.4)] bg-surface-primary z-500"
        >
          <div className="flex items-center justify-around">
            {navItems.map(
              (item) =>
                item.IconComponent && (
                  <BottomNavItemClient
                    key={item.key}
                    IconComponent={item.IconComponent}
                    label={item.label}
                    href={item.href}
                    isSelected={selected === item.key}
                    supportsFilledState={item.supportsFilledState}
                    badge={item.badge?.toString() || undefined}
                  />
                )
            )}
          </div>
        </motion.nav>
      )}
    </AnimatePresence>
  );
}
