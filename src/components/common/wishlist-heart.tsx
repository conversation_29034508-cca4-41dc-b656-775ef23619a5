'use client';
import { HeartIcon } from '../icons/heart-icon';
import { useWishlistProductMutation } from '@/mutations/product.mutations';
import { useGlobalDataStore } from '@/store/global-data.store';
import { useGlobalActionsStore } from '@/store/global-actions.store';
import { motion } from 'motion/react';

export const WishlistHeart = ({
  className,
  wishlisted,
  productId,
  sizeId,
  widgetId,
}: {
  className?: string;
  wishlisted: boolean;
  productId: string;
  sizeId?: string;
  widgetId: string;
}) => {
  const { mutate: addToWishlist } = useWishlistProductMutation();
  const { globalData } = useGlobalDataStore();
  const { loginSheet } = useGlobalActionsStore();
  const { user } = globalData;

  const handleWishlistClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    e.preventDefault();

    if (!user) {
      loginSheet.show();
      return;
    }

    addToWishlist({
      style_color_id: productId,
      inactive: wishlisted,
      sizeId,
      widgetId,
    });
  };

  return (
    <div className={className} onClick={handleWishlistClick}>
      <motion.div animate={{ scale: wishlisted ? [1, 1.2, 1] : 1 }} transition={{ duration: 0.3 }}>
        <HeartIcon
          fill={wishlisted ? '#f5f5f5' : 'var(--content-primary)'}
          className="h-16 w-16"
          type={wishlisted ? 'filled' : 'outline'}
        />
      </motion.div>
    </div>
  );
};
