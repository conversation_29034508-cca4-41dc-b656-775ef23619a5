'use client';
import { useGlobalActionsStore } from '@/store/global-actions.store';
import { motion, AnimatePresence } from 'motion/react';
import { X } from 'lucide-react';
import { Button } from '../ui/button';
import { useTheme } from 'next-themes';
import Link from 'next/link';
import { useCallApi } from '@/queries/action.queries';
import { useRouter } from 'next/navigation';

export default function FullScreenBanner() {
  const { fullScreenBanner } = useGlobalActionsStore();
  const { theme } = useTheme();
  const router = useRouter();
  const isDarkMode = theme === 'dark';
  let backgroundImageUrl = '';

  if (isDarkMode) {
    backgroundImageUrl = fullScreenBanner.data.ui_data?.bg_image?.media_url?.d || '';
  } else {
    backgroundImageUrl = fullScreenBanner.data.ui_data?.bg_image?.media_url?.l || '';
  }

  useCallApi(fullScreenBanner.data.seen_endpoint, fullScreenBanner.data.seen_endpoint_method);

  return (
    <AnimatePresence>
      {fullScreenBanner.isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
          className={`fixed inset-0 z-1000 flex flex-col items-center justify-between w-376 h-[100dvh] left-1/2 -translate-x-1/2 ${fullScreenBanner.data.ui_data?.bg_color ? `bg-${fullScreenBanner.data.ui_data.bg_color}` : 'bg-surface-primary'}`}
          style={{
            backgroundImage: backgroundImageUrl ? `url(${backgroundImageUrl})` : undefined,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
          }}
        >
          <div className="px-16 h-56 w-full flex items-center">
            <X className="w-32 h-32 cursor-pointer" onClick={() => fullScreenBanner.hide()} />
          </div>

          <div className="px-16 pb-32 pt-16 w-full">
            <Link href={fullScreenBanner.data.cta_url}>
              <Button
                className="w-full h-48"
                onClick={() => {
                  fullScreenBanner.hide();
                  router.push(fullScreenBanner.data.cta_url);
                }}
              >
                {fullScreenBanner.data.cta}
              </Button>
            </Link>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
