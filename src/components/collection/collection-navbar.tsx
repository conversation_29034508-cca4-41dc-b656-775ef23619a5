'use client';

import SearchOutline from '../icons/search-outline';
import HeartOutline from '../icons/heart-outline';
import ProfileOutline from '../icons/profile-outline';
import Link from 'next/link';

export default function CollectionNavbar() {
  return (
    <div
      className="w-full h-52 flex justify-between items-center px-16 z-1000"
      style={{
        lineHeight: '20 * var(--scale)',
      }}
    >
      <div className="">
        <p className="text-2xl font-semibold">Collection</p>
      </div>
      <div className="flex gap-16">
        <Link href="/search">
          <SearchOutline className="size-24" fill="#FAFAFA" />
        </Link>
        <Link href="/collections/wishlist">
          <HeartOutline className="size-24" fill="#FAFAFA" />
        </Link>
        <Link href="/profile_page">
          <ProfileOutline className="size-24 cursor-pointer" fill="#FAFAFA" />
        </Link>
      </div>
    </div>
  );
}
