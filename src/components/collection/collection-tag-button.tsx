'use client';

import * as Tabs from '@radix-ui/react-tabs';
import { useTagWithUrlSupport } from '@/store/tag';
import Link from 'next/link';

export default function CollectionTagButton({ tag }: { tag: 'man' | 'woman' | 'brands' }) {
  const { tag: currentTag, setTag } = useTagWithUrlSupport();
  const config = getTagConfig(tag);
  const isSelected = currentTag === tag;

  return (
    <div>
      <Tabs.Trigger
        value={tag}
        className={`cursor-pointer text-sm font-medium transition-all duration-200 border rounded-sm w-107 h-32 ${isSelected ? `${config.themeColor} ${config.borderColor}` : 'bg-surface-secondary'}`}
        onClick={(e) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('🎯 Collection Tag Button - Clicked:', tag);
          }
          e.preventDefault();
          e.stopPropagation();
          setTag(tag);
        }}
      >
        <p className="text-xs">{config.label}</p>
      </Tabs.Trigger>

      {/* Non-JavaScript fallback */}
      <noscript>
        <Link href={`?shop=${tag}`} className="absolute inset-0 block" />
      </noscript>
    </div>
  );
}

const getTagConfig = (tagType: 'man' | 'woman' | 'brands') => {
  const configs = {
    man: {
      label: 'MAN',
      borderColor: 'border-[var(--theme-blue-500)]',
      themeColor: 'bg-[#4C81C54D]',
    },
    woman: {
      label: 'WOMAN',
      borderColor: 'border-[var(--theme-red-500)]',
      themeColor: 'bg-[#EF44444D]',
    },
    brands: {
      label: 'ALL BRANDS',
      borderColor: 'border-[var(--theme-yellow-500)]',
      themeColor: 'bg-[#DA8D0A4D]',
    },
  };

  return configs[tagType];
};
