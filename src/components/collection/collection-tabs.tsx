'use client';

import CollectionTagButton from './collection-tag-button';
import TabContent from './collection-tab-content';
import CollectionNavbar from './collection-navbar';
import { useInitialCollectionData } from '@/queries/use-collection-data';
import { useTagWithUrlSupport } from '@/store/tag';

export default function CollectionTabs({ defaultTab }: { defaultTab: string | undefined }) {
  const { data } = useInitialCollectionData();
  const defaultTag = data?.global_state?.default_tag;
  const { tag } = useTagWithUrlSupport();

  // Determine current active tab based on URL
  const currentTab = tag || defaultTab || defaultTag || 'man';

  return (
    <>
      <CollectionNavbar />
      <div className="w-full">
        <div className="flex w-full pl-4 mb-16 justify-center gap-12">
          <CollectionTagButton tag="man" />
          <CollectionTagButton tag="woman" />
          <CollectionTagButton tag="brands" />
        </div>

        <div className="relative">
          {currentTab === 'man' && (
            <div className="w-full">
              <TabContent type="man" />
            </div>
          )}
          {currentTab === 'woman' && (
            <div className="w-full">
              <TabContent type="woman" />
            </div>
          )}
          {currentTab === 'brands' && (
            <div className="w-full">
              <TabContent type="brands" />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
