'use client';

import * as Tabs from '@radix-ui/react-tabs';
import TagButton from '../common/tag-button';
import TabContent from './tab-content';
import SearchOutline from '../icons/search-outline';
import HeartOutline from '../icons/heart-outline';
import ProfileOutline from '../icons/profile-outline';
import Link from 'next/link';
import { useInitialTrendsData } from '@/queries/use-trends-data';
import DragScrollContainer from '../common/drag-scroll-container';
import { useTagWithUrlSupport } from '@/store/tag';

export default function TrendsTabs({ defaultTab }: { defaultTab: string | undefined }) {
  const { data } = useInitialTrendsData();
  const { tag } = useTagWithUrlSupport();

  // Stable tab value to prevent flickering - prioritize server-provided defaultTab for SSR consistency
  const currentTab = defaultTab || tag || data?.global_state?.default_tag || 'man';

  return (
    <DragScrollContainer className="flex-1 overflow-y-scroll bg-surface-primary main-page z-100">
      <Tabs.Root value={currentTab} className="w-full">
        {/* Fixed AppBar with tabs */}
        <div className="sticky top-0 z-50 border-b border-b-divider-small px-8 bg-surface-primary">
          <div className="flex justify-between items-center">
            <Tabs.List>
              <div className="flex w-full justify-start pl-4">
                <TagButton tag="man" fromTrends={true} />
                <TagButton tag="woman" fromTrends={true} />
              </div>
            </Tabs.List>
            <div className="flex gap-16">
              <Link href="/search">
                <SearchOutline className="size-24" fill="#FAFAFA" />
              </Link>
              <Link href="/collections/wishlist">
                <HeartOutline className="size-24" fill="#FAFAFA" />
              </Link>
              <Link href="/profile_page">
                <ProfileOutline className="size-24 cursor-pointer" fill="#FAFAFA" />
              </Link>
            </div>
          </div>
        </div>

        <div>
          <Tabs.Content
            value="man"
            className="w-full data-[state=inactive]:hidden mb-72"
            forceMount
          >
            <TabContent type="man" />
          </Tabs.Content>
          <Tabs.Content
            value="woman"
            className="w-full data-[state=inactive]:hidden mb-80"
            forceMount
          >
            <TabContent type="woman" />
          </Tabs.Content>
        </div>
      </Tabs.Root>
    </DragScrollContainer>
  );
}
