import { IconProps } from '@/types/icon';

export default function OfficeIcon({ className, fill = '#FAFAFA' }: IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.48554 5.07765C6.66668 4.69807 6.96031 4.38671 7.32456 4.18797C7.49009 4.09841 7.70858 4.03666 8.09114 4.0034C8.48242 3.96939 8.98536 3.96884 9.70901 3.96884H14.2909C15.0145 3.96884 15.5175 3.96939 15.9087 4.0034C16.2912 4.03666 16.5097 4.09839 16.6752 4.18793C17.0396 4.38685 17.3332 4.69843 17.5142 5.07823L17.5151 5.08013C17.6034 5.26375 17.6628 5.50446 17.6945 5.91641C17.7268 6.33529 17.7273 6.87267 17.7273 7.6397V16.3601C17.7273 17.1272 17.7268 17.6646 17.6945 18.0835C17.6628 18.4956 17.6033 18.7367 17.5149 18.9209L17.5142 18.9225C17.3333 19.3021 17.0398 19.6136 16.6757 19.8125C16.5101 19.9018 16.2914 19.9634 15.9089 19.9965C15.6003 20.0233 15.2223 20.0293 14.7216 20.0306C14.7449 19.9628 14.7576 19.8899 14.7576 19.814V15.0387C14.7576 14.4055 14.5118 13.7982 14.0742 13.3504C13.6366 12.9027 13.0431 12.6511 12.4243 12.6511H11.5758C10.9569 12.6511 10.3634 12.9027 9.92585 13.3504C9.48826 13.7982 9.24243 14.4055 9.24243 15.0387V19.814C9.24243 19.8899 9.25513 19.9628 9.27849 20.0306C8.77764 20.0293 8.39967 20.0233 8.09114 19.9964C7.70857 19.9632 7.49008 19.9014 7.32455 19.8119C6.96027 19.6129 6.66668 19.3014 6.48568 18.9216L6.48477 18.9197C6.39653 18.7361 6.33705 18.4954 6.30533 18.0834C6.27308 17.6646 6.27262 17.1272 6.27262 16.3601V7.6397C6.27262 6.87267 6.27308 6.33528 6.30533 5.91629C6.33705 5.50427 6.39653 5.26315 6.48493 5.07891L6.48554 5.07765ZM10.479 20.031H13.5211C13.4976 19.9631 13.4849 19.8901 13.4849 19.814V15.0387C13.4849 14.7509 13.3731 14.4749 13.1742 14.2713C12.9753 14.0678 12.7056 13.9535 12.4243 13.9535H11.5758C11.2945 13.9535 11.0247 14.0678 10.8258 14.2713C10.6269 14.4749 10.5152 14.7509 10.5152 15.0387V19.814C10.5152 19.8901 10.5024 19.9631 10.479 20.031ZM14.3191 2.6665H9.68082C8.99201 2.6665 8.4349 2.66649 7.9834 2.70574C7.51755 2.74624 7.10737 2.83196 6.72825 3.03742L6.72688 3.03816C6.12577 3.36583 5.64115 3.87939 5.34217 4.50556C5.15334 4.89938 5.07415 5.3253 5.03653 5.81401C4.99987 6.29018 4.99987 6.87892 4.99988 7.6126V16.3873C4.99987 17.1209 4.99987 17.7097 5.03653 18.1857C5.07414 18.6742 5.1533 19.1 5.34219 19.4934C5.641 20.1199 6.12557 20.6337 6.72675 20.9616L6.72825 20.9624C7.10737 21.1679 7.51755 21.2536 7.9834 21.2941C8.4349 21.3333 8.99203 21.3333 9.68086 21.3333H14.3189C15.0078 21.3333 15.5649 21.3333 16.0163 21.2942C16.482 21.2539 16.892 21.1684 17.2709 20.9637L17.2731 20.9625C17.8743 20.6346 18.3588 20.1208 18.6576 19.4945C18.8465 19.1006 18.9257 18.6746 18.9634 18.1858C19 17.7097 19 17.1209 19 16.3873V16.3872V7.61259C19 6.87893 19 6.29017 18.9634 5.81411C18.9257 5.32567 18.8466 4.89988 18.6577 4.5064C18.3589 3.87996 17.8743 3.36612 17.2731 3.03823L17.2716 3.03742C16.8925 2.83197 16.4823 2.74624 16.0165 2.70574C15.565 2.66649 15.0079 2.6665 14.3191 2.6665ZM8.60606 6.13945C8.2546 6.13945 7.96969 6.43098 7.96969 6.79062C7.96969 7.15025 8.2546 7.44178 8.60606 7.44178H9.36969C9.72115 7.44178 10.0061 7.15025 10.0061 6.79062C10.0061 6.43098 9.72115 6.13945 9.36969 6.13945H8.60606ZM13.697 6.13945C13.3456 6.13945 13.0606 6.43098 13.0606 6.79062C13.0606 7.15025 13.3456 7.44178 13.697 7.44178H15.394C15.7455 7.44178 16.0304 7.15025 16.0304 6.79062C16.0304 6.43098 15.7455 6.13945 15.394 6.13945H13.697ZM8.60606 9.61234C8.2546 9.61234 7.96969 9.90388 7.96969 10.2635C7.96969 10.6231 8.2546 10.9147 8.60606 10.9147H10.303C10.6545 10.9147 10.9394 10.6231 10.9394 10.2635C10.9394 9.90388 10.6545 9.61234 10.303 9.61234H8.60606ZM13.697 9.61234C13.3456 9.61234 13.0606 9.90388 13.0606 10.2635C13.0606 10.6231 13.3456 10.9147 13.697 10.9147H15.394C15.7455 10.9147 16.0304 10.6231 16.0304 10.2635C16.0304 9.90388 15.7455 9.61234 15.394 9.61234H13.697Z"
        fill={fill}
      />
    </svg>
  );
}
