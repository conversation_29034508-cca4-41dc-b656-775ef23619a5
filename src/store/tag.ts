'use client';

import { useSearchParams, usePathname } from 'next/navigation';
import { useAppConfigStore } from './app-config';
import { setCookie, getCookie } from 'cookies-next/client';
import { useEffect } from 'react';

export function useTagWithUrlSupport() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { updateConfig, config, isHydrated } = useAppConfigStore();

  // Get tag from URL
  const urlTag = searchParams?.get('shop') as 'man' | 'woman' | 'brands' | null;

  // Synchronize X-User-Pref cookie with app config store when hydrated
  useEffect(() => {
    if (isHydrated) {
      const currentCookie = getCookie('X-User-Pref');
      const userPreference = config.userPreference;

      // If cookie doesn't exist or doesn't match app config, sync it
      if (currentCookie !== userPreference) {
        setCookie('X-User-Pref', userPreference);
        if (process.env.NODE_ENV === 'development') {
          console.log(
            '🔧 Tag Store - Syncing X-User-Pref cookie:',
            currentCookie,
            '→',
            userPreference
          );
        }
      }
    }
  }, [isHydrated, config.userPreference]);

  // Only update X-Is-Brand-Selected cookie when on collection page
  const isCollectionPage = pathname === '/collection';

  if (isCollectionPage) {
    // Initialize cookie if it doesn't exist or ensure it matches URL
    const brandSelectedCookie = getCookie('X-Is-Brand-Selected');
    const expectedValue = urlTag === 'brands' ? 'true' : 'false';

    if (brandSelectedCookie === undefined || brandSelectedCookie !== expectedValue) {
      setCookie('X-Is-Brand-Selected', expectedValue);
      if (process.env.NODE_ENV === 'development') {
        console.log(
          '🔧 Tag Store - Setting X-Is-Brand-Selected to:',
          expectedValue,
          'based on urlTag:',
          urlTag,
          '(was:',
          brandSelectedCookie,
          ')'
        );
      }
    }
  }

  // Wrapper for setTag that also updates URL
  const setTagWithUrl = (tag: 'man' | 'woman' | 'brands') => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Tag Store - setTag called with:', tag, 'on page:', pathname);
    }

    // Update URL without refetch
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('shop', tag);
    window.history.pushState({ path: newUrl.href }, '', newUrl.href);

    if (tag !== 'brands') {
      updateConfig({ userPreference: tag });
      setCookie('X-User-Pref', tag);

      // Only update X-Is-Brand-Selected cookie on collection page
      if (isCollectionPage) {
        setCookie('X-Is-Brand-Selected', 'false');
        if (process.env.NODE_ENV === 'development') {
          console.log('🔧 Tag Store - Set X-Is-Brand-Selected to false for tag:', tag);
        }
      }
    } else {
      // When selecting brands, don't clear X-User-Pref - preserve existing preference
      // setCookie('X-User-Pref', null); // ← Removed this line

      // Only update X-Is-Brand-Selected cookie on collection page
      if (isCollectionPage) {
        setCookie('X-Is-Brand-Selected', 'true');
        if (process.env.NODE_ENV === 'development') {
          console.log('🔧 Tag Store - Set X-Is-Brand-Selected to true for brands');
        }
      }
    }

    // Verify cookie was set (only on collection page)
    if (process.env.NODE_ENV === 'development' && isCollectionPage) {
      const verifyValue = getCookie('X-Is-Brand-Selected');
      console.log('🔧 Tag Store - Cookie verification after setting:', verifyValue);
    }
  };

  return {
    tag: urlTag,
    setTag: setTagWithUrl,
  };
}
