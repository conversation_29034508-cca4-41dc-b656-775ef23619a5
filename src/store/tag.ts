'use client';

import { useSearchParams, usePathname } from 'next/navigation';
import { useAppConfigStore } from './app-config';
import { setCookie, getCookie } from 'cookies-next/client';
import { useEffect, useMemo } from 'react';

export function useTagWithUrlSupport() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { updateConfig, config, isHydrated } = useAppConfigStore();

  // Get tag from URL
  const urlTag = searchParams?.get('shop') as 'man' | 'woman' | 'brands' | null;

  // Determine current tag based on priority: URL → stored preference → default
  const currentTag = useMemo(() => {
    if (urlTag) return urlTag;
    if (isHydrated && config.userPreference) return config.userPreference;
    return 'man'; // fallback default
  }, [urlTag, isHydrated, config.userPreference]);

  // Synchronize cookies with current state
  useEffect(() => {
    if (!isHydrated) return;

    const isCollectionPage = pathname === '/collection';

    // Sync X-User-Pref cookie (only for man/woman preferences)
    if (currentTag !== 'brands') {
      const currentCookie = getCookie('X-User-Pref');
      if (currentCookie !== currentTag) {
        setCookie('X-User-Pref', currentTag);
        if (process.env.NODE_ENV === 'development') {
          console.log('🔧 Tag Store - Syncing X-User-Pref cookie:', currentCookie, '→', currentTag);
        }
      }
    }

    // ONLY sync X-Is-Brand-Selected cookie when on collection page
    if (isCollectionPage) {
      const brandSelectedCookie = getCookie('X-Is-Brand-Selected');
      const expectedValue = currentTag === 'brands' ? 'true' : 'false';

      if (brandSelectedCookie !== expectedValue) {
        setCookie('X-Is-Brand-Selected', expectedValue);
        if (process.env.NODE_ENV === 'development') {
          console.log(
            '🔧 Tag Store - Setting X-Is-Brand-Selected to:',
            expectedValue,
            '(collection page only)'
          );
        }
      }
    }
    // Do NOT touch X-Is-Brand-Selected cookie when not on collection page
  }, [isHydrated, currentTag, pathname]);

  // Simplified setTag function
  const setTag = (tag: 'man' | 'woman' | 'brands') => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Tag Store - setTag called with:', tag, 'on page:', pathname);
    }

    // Update URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('shop', tag);
    window.history.pushState({ path: newUrl.href }, '', newUrl.href);

    // Update app config store for man/woman preferences
    if (tag !== 'brands') {
      updateConfig({ userPreference: tag });
    }
  };

  return {
    tag: currentTag,
    setTag,
    // Helper to get user preference for navigation (excludes brands)
    userPreference: config.userPreference || 'man',
  };
}
